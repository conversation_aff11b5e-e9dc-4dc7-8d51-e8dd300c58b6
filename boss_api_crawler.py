"""
BOSS直聘API接口爬虫
直接调用BOSS直聘的内部API获取职位数据
"""

import asyncio
import aiohttp
import json
import time
import random
from typing import List, Dict, Set
import re

class BOSSAPICrawler:
    """BOSS直聘API爬虫"""
    
    def __init__(self):
        self.base_url = "https://www.zhipin.com"
        self.api_base = "https://www.zhipin.com/wapi/zpgeek"
        self.session = None
        self.collected_urls = set()
        
        # 常用请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.zhipin.com/web/geek/job',
            'Origin': 'https://www.zhipin.com',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }
        
        # 城市代码
        self.city_codes = {
            "北京": "101010100",
            "上海": "101020100", 
            "深圳": "101280600",
            "杭州": "101210100",
            "广州": "101280100",
            "南京": "101190100"
        }
        
        # 关键词
        self.keywords = [
            "Python", "Java", "JavaScript", "前端", "后端", "产品经理"
        ]

    async def __aenter__(self):
        """创建aiohttp会话"""
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """关闭会话"""
        if self.session:
            await self.session.close()

    async def get_job_list_api(self, city_code: str = "", keyword: str = "", page: int = 1) -> Dict:
        """调用职位列表API"""
        try:
            # 构造API URL
            api_url = f"{self.api_base}/search/joblist.json"
            
            # API参数
            params = {
                'scene': '1',
                'query': keyword,
                'city': city_code,
                'experience': '',
                'payType': '',
                'partTime': '',
                'degree': '',
                'industry': '',
                'scale': '',
                'stage': '',
                'position': '',
                'jobType': '',
                'salary': '',
                'multiBusinessDistrict': '',
                'multiSubway': '',
                'page': str(page),
                'pageSize': '30'
            }
            
            # 过滤空参数
            params = {k: v for k, v in params.items() if v}
            
            print(f"🔍 调用API: {api_url}")
            print(f"📋 参数: {params}")
            
            async with self.session.get(api_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ API调用成功，状态码: {response.status}")
                    return data
                else:
                    print(f"❌ API调用失败，状态码: {response.status}")
                    text = await response.text()
                    print(f"响应内容: {text[:500]}")
                    return {}
                    
        except Exception as e:
            print(f"❌ API调用异常: {str(e)}")
            return {}

    def extract_job_urls_from_api_response(self, api_data: Dict) -> List[str]:
        """从API响应中提取职位URL"""
        job_urls = []
        
        try:
            # 检查API响应结构
            if 'zpData' in api_data and 'jobList' in api_data['zpData']:
                job_list = api_data['zpData']['jobList']
                
                for job in job_list:
                    if 'securityId' in job:
                        # 构造职位详情URL
                        job_url = f"https://www.zhipin.com/job_detail/{job['securityId']}.html"
                        job_urls.append(job_url)
                        
                        # 打印职位信息
                        job_name = job.get('jobName', '未知职位')
                        company_name = job.get('brandName', '未知公司')
                        salary = job.get('salaryDesc', '薪资面议')
                        print(f"📋 {job_name} - {company_name} - {salary}")
                
                print(f"✅ 从API响应提取到 {len(job_urls)} 个职位URL")
                
            elif 'code' in api_data:
                print(f"⚠️ API返回错误码: {api_data.get('code')}")
                print(f"错误信息: {api_data.get('message', '未知错误')}")
                
            else:
                print("⚠️ API响应格式不符合预期")
                print(f"响应键: {list(api_data.keys())}")
                
        except Exception as e:
            print(f"❌ 解析API响应异常: {str(e)}")
            
        return job_urls

    async def crawl_search_with_api(self, city_code: str = "", keyword: str = "", max_pages: int = 3) -> List[str]:
        """使用API爬取搜索结果"""
        all_urls = []
        
        for page in range(1, max_pages + 1):
            print(f"\n📄 API爬取第 {page} 页 (城市: {city_code or '全国'}, 关键词: {keyword or '全部'})")
            
            # 调用API
            api_data = await self.get_job_list_api(city_code, keyword, page)
            
            if not api_data:
                print(f"⚠️ 第 {page} 页API调用失败，停止翻页")
                break
            
            # 提取URL
            page_urls = self.extract_job_urls_from_api_response(api_data)
            
            if not page_urls:
                print(f"⚠️ 第 {page} 页无职位数据，停止翻页")
                break
                
            all_urls.extend(page_urls)
            
            # 检查是否还有更多页面
            if 'zpData' in api_data and 'hasMore' in api_data['zpData']:
                if not api_data['zpData']['hasMore']:
                    print(f"✅ 已到最后一页")
                    break
            
            # 随机延迟
            await asyncio.sleep(random.uniform(1, 3))
        
        return all_urls

    async def crawl_all_job_urls_api(self, cities: List[str] = None, keywords: List[str] = None, max_pages: int = 3) -> Set[str]:
        """使用API爬取所有职位URL"""
        if cities is None:
            cities = list(self.city_codes.keys())[:2]
        if keywords is None:
            keywords = self.keywords[:2]
        
        print(f"🚀 开始API爬取所有职位URL")
        print(f"📊 城市: {cities}")
        print(f"📊 关键词: {keywords}")
        print("=" * 50)
        
        # 策略1: 城市+关键词组合
        for city in cities:
            city_code = self.city_codes.get(city, "")
            for keyword in keywords:
                print(f"\n🎯 API搜索: {city} + {keyword}")
                urls = await self.crawl_search_with_api(city_code, keyword, max_pages)
                self.collected_urls.update(urls)
                print(f"📈 当前累计URL数: {len(self.collected_urls)}")
        
        # 策略2: 仅城市搜索
        for city in cities:
            city_code = self.city_codes.get(city, "")
            print(f"\n🎯 API搜索: {city} (全部职位)")
            urls = await self.crawl_search_with_api(city_code, "", max_pages)
            self.collected_urls.update(urls)
            print(f"📈 当前累计URL数: {len(self.collected_urls)}")
        
        return self.collected_urls

    def save_urls_to_file(self, filename: str = "boss_job_urls_api.txt") -> str:
        """保存URL到文件"""
        import os
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        sorted_urls = sorted(list(self.collected_urls))
        
        with open(filepath, 'w', encoding='utf-8') as f:
            for url in sorted_urls:
                f.write(url + '\n')
        
        print(f"\n💾 URL已保存到: {filepath}")
        print(f"📊 总计URL数量: {len(sorted_urls)}")
        
        return filepath

    def print_statistics(self):
        """打印统计信息"""
        print("\n" + "=" * 50)
        print("📊 API爬取统计")
        print("=" * 50)
        print(f"✨ 获取URL总数: {len(self.collected_urls)}")
        print("=" * 50)

# 使用示例
async def main():
    """主函数"""
    async with BOSSAPICrawler() as crawler:
        # 使用API爬取职位URL
        urls = await crawler.crawl_all_job_urls_api(
            cities=["北京", "上海"],
            keywords=["Python", "Java"],
            max_pages=2
        )
        
        # 保存结果
        crawler.save_urls_to_file()
        
        # 打印统计
        crawler.print_statistics()

if __name__ == "__main__":
    asyncio.run(main())
