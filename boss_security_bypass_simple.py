"""
简化版BOSS安全绕过模块
基于Position_Crawler_1项目的核心思路
"""

import asyncio
import random
import time
from typing import Optional

async def boss_security_bypass(url: str) -> Optional[str]:
    """
    简化版BOSS安全绕过方法
    基于Position_Crawler_1项目的核心策略
    """
    try:
        # 导入必要的库
        from crawl4ai import AsyncWebCrawler, BrowserConfig
        
        # 配置高级反检测浏览器
        config = BrowserConfig(
            headless=True,
            browser_type="chromium",
            viewport_width=1920,
            viewport_height=1080,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            extra_args=[
                # 核心反检测参数
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-automation",
                "--disable-client-side-phishing-detection",
                "--disable-component-extensions-with-background-pages",
                "--disable-default-apps",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                "--metrics-recording-only",
                "--no-first-run",
                "--safebrowsing-disable-auto-update",
                "--password-store=basic",
                "--use-mock-keychain",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",
                "--disable-gpu",
                
                # 模拟真实环境
                "--enable-features=NetworkService,NetworkServiceLogging",
                "--force-device-scale-factor=1",
                "--lang=zh-CN",
                "--accept-lang=zh-CN,zh;q=0.9,en;q=0.8"
            ],
            headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache",
                "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1",
                "Referer": "https://www.zhipin.com/"
            },
            cookies=[
                {
                    "name": "__zp_stoken__",
                    "value": f"token_{random.randint(100000000, 999999999)}",
                    "domain": ".zhipin.com",
                    "path": "/"
                },
                {
                    "name": "Hm_lvt_194df3105ad7148dcf2b98a91b5e727a",
                    "value": str(int(time.time())),
                    "domain": ".zhipin.com",
                    "path": "/"
                }
            ]
        )
        
        async with AsyncWebCrawler(config=config) as crawler:
            # 多阶段访问策略
            
            # 阶段1: 预热访问首页
            print("🔄 阶段1: 预热访问")
            await crawler.arun(
                url="https://www.zhipin.com",
                timeout=15000,
                delay_before_return_html=2000
            )
            
            # 随机延迟
            await asyncio.sleep(random.uniform(1, 3))
            
            # 阶段2: 访问目标页面
            print("🔄 阶段2: 访问目标页面")
            result = await crawler.arun(
                url=url,
                timeout=45000,
                delay_before_return_html=5000,
                js_code="""
                // 深度反检测脚本
                (function() {
                    // 隐藏webdriver属性
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                    
                    // 模拟真实插件
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5],
                    });
                    
                    // 设置语言
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['zh-CN', 'zh', 'en'],
                    });
                    
                    // 隐藏自动化标识
                    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
                })();
                
                // 模拟真实用户行为
                const simulateUserBehavior = async () => {
                    const randomDelay = () => Math.random() * 1000 + 500;
                    
                    // 随机滚动
                    await new Promise(resolve => setTimeout(resolve, randomDelay()));
                    window.scrollTo(0, window.innerHeight / 3);
                    
                    await new Promise(resolve => setTimeout(resolve, randomDelay()));
                    window.scrollTo(0, window.innerHeight * 2 / 3);
                    
                    await new Promise(resolve => setTimeout(resolve, randomDelay()));
                    window.scrollTo(0, document.body.scrollHeight);
                    
                    await new Promise(resolve => setTimeout(resolve, randomDelay()));
                    window.scrollTo(0, 0);
                    
                    // 触发事件
                    const scrollEvent = new Event('scroll');
                    window.dispatchEvent(scrollEvent);
                    
                    const resizeEvent = new Event('resize');
                    window.dispatchEvent(resizeEvent);
                    
                    await new Promise(resolve => setTimeout(resolve, 1000));
                };
                
                await simulateUserBehavior();
                """
            )
            
            if result.success and result.html:
                print("✅ 安全绕过成功")
                return result.html
            else:
                print("❌ 安全绕过失败")
                return None
                
    except Exception as e:
        print(f"❌ 安全绕过异常: {e}")
        return None

async def test_security_bypass():
    """测试安全绕过功能"""
    test_url = "https://www.zhipin.com/web/geek/job?query=Python"
    
    print("🧪 测试BOSS安全绕过功能")
    print(f"🎯 测试URL: {test_url}")
    
    html_content = await boss_security_bypass(test_url)
    
    if html_content:
        print(f"✅ 测试成功，获取页面长度: {len(html_content)}")
        
        # 检查是否包含职位数据
        if 'job_detail' in html_content or '职位' in html_content:
            print("✅ 页面包含职位数据")
        else:
            print("⚠️ 页面不包含职位数据")
            
        # 保存测试结果
        with open("test_security_bypass.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        print("💾 测试结果已保存到 test_security_bypass.html")
        
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    asyncio.run(test_security_bypass())
