"""
BOSS直聘职位URL爬虫
基于Position_Crawler_1项目核心技术
专门用于抓取所有正在招聘中的职位URL
集成BOSS安全检查绕过引擎
"""

import asyncio
import time
import random
import json
import os
from typing import List, Dict, Set, Any
from urllib.parse import urljoin, urlparse, parse_qs
import re

# 导入核心依赖
try:
    from crawl4ai import AsyncWebCrawler, BrowserConfig
    CRAWL4AI_AVAILABLE = True
except ImportError:
    print("❌ crawl4ai未安装，请运行: pip install crawl4ai")
    CRAWL4AI_AVAILABLE = False

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    print("❌ beautifulsoup4未安装，请运行: pip install beautifulsoup4")
    BS4_AVAILABLE = False

# 导入BOSS安全检查绕过器（从Position_Crawler_1项目学习）
try:
    import sys
    sys.path.append('/Users/<USER>/Position_Crawler_1')
    from boss_security_bypass import boss_security_bypass
    BOSS_SECURITY_BYPASS_AVAILABLE = True
    print("🚀 BOSS安全检查绕过器已加载（原版）")
except ImportError as e:
    print(f"⚠️ 原版BOSS安全检查绕过器导入失败: {e}")
    try:
        from boss_security_bypass_simple import boss_security_bypass
        BOSS_SECURITY_BYPASS_AVAILABLE = True
        print("🚀 BOSS安全检查绕过器已加载（简化版）")
    except ImportError as e2:
        print(f"❌ 简化版BOSS安全检查绕过器也导入失败: {e2}")
        BOSS_SECURITY_BYPASS_AVAILABLE = False

class BOSSURLCrawler:
    """BOSS直聘职位URL专用爬虫"""
    
    def __init__(self):
        self.base_url = "https://www.zhipin.com"
        self.search_url = "https://www.zhipin.com/web/geek/job"
        self.crawler = None
        self.collected_urls = set()
        self.semaphore = asyncio.Semaphore(3)  # 控制并发数
        
        # 城市代码映射（主要城市）
        self.city_codes = {
            "北京": "101010100", "上海": "101020100", "广州": "101280100",
            "深圳": "101280600", "杭州": "101210100", "南京": "101190100",
            "武汉": "101200100", "成都": "101270100", "西安": "101110100",
            "重庆": "101040100", "天津": "101030100", "苏州": "101190400",
            "郑州": "101180100", "长沙": "101250100", "青岛": "101120200",
            "无锡": "101190200", "大连": "101070200", "宁波": "101210400",
            "佛山": "101280800", "东莞": "101280800"
        }
        
        # 热门职位关键词
        self.job_keywords = [
            "Python", "Java", "JavaScript", "前端", "后端", "算法工程师",
            "数据分析", "产品经理", "UI设计师", "运营", "销售", "市场",
            "人工智能", "机器学习", "大数据", "云计算", "区块链",
            "移动开发", "测试工程师", "运维工程师", "架构师"
        ]
        
        # 用户代理池
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0"
        ]
        
        # 统计信息
        self.stats = {
            'total_pages_crawled': 0,
            'total_urls_found': 0,
            'unique_urls': 0,
            'failed_requests': 0,
            'start_time': None,
            'end_time': None
        }

    async def __aenter__(self):
        """异步上下文管理器入口"""
        if not CRAWL4AI_AVAILABLE:
            raise ImportError("crawl4ai不可用")

        # 配置浏览器选项（基于Position_Crawler_1的优化配置）
        browser_config = BrowserConfig(
            headless=True,
            browser_type="chromium",
            viewport_width=1920,
            viewport_height=1080,
            user_agent=random.choice(self.user_agents),
            extra_args=[
                # 基础反检测
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",  # 禁用图片加载提高速度
                "--disable-gpu",

                # 深度反检测（从Position_Crawler_1学习）
                "--exclude-switches=enable-automation",
                "--disable-automation",
                "--disable-client-side-phishing-detection",
                "--disable-component-extensions-with-background-pages",
                "--disable-default-apps",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                "--metrics-recording-only",
                "--no-first-run",
                "--safebrowsing-disable-auto-update",
                "--password-store=basic",
                "--use-mock-keychain",

                # 模拟真实用户环境
                "--enable-features=NetworkService,NetworkServiceLogging",
                "--force-device-scale-factor=1",
                "--lang=zh-CN",
                "--accept-lang=zh-CN,zh;q=0.9,en;q=0.8"
            ],
            headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache",
                "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1"
            },
            cookies=[
                {
                    "name": "__zp_stoken__",
                    "value": f"token_{random.randint(100000000, 999999999)}",
                    "domain": ".zhipin.com",
                    "path": "/"
                }
            ]
        )

        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.crawler:
            await self.crawler.__aexit__(exc_type, exc_val, exc_tb)

    def extract_job_urls_from_html(self, html_content: str) -> List[str]:
        """从HTML内容中提取职位URL"""
        if not BS4_AVAILABLE:
            return []
            
        soup = BeautifulSoup(html_content, 'html.parser')
        job_urls = []
        
        # 多种选择器策略
        selectors = [
            "a[href*='/job_detail/']",
            "a[href*='job_detail']", 
            ".job-list a[href*='job']",
            ".job-item a[href*='job']",
            "a[data-jobid]",
            "a[ka='search_list_job']"
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href and '/job_detail/' in href:
                    # 构造完整URL
                    if href.startswith('/'):
                        full_url = urljoin(self.base_url, href)
                    else:
                        full_url = href
                    
                    # 验证URL格式
                    if self.is_valid_job_url(full_url):
                        job_urls.append(full_url)
        
        return list(set(job_urls))  # 去重

    def is_valid_job_url(self, url: str) -> bool:
        """验证是否为有效的职位URL"""
        if not url:
            return False

        # 检查URL格式
        pattern = r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]+\.html'
        return bool(re.match(pattern, url))

    async def _try_multiple_access_methods(self, url: str) -> str:
        """尝试多种方法访问页面（基于Position_Crawler_1的策略）"""
        methods = [
            self._method_crawl4ai_standard,
            self._method_crawl4ai_with_js,
            self._method_crawl4ai_with_security_bypass,
            self._method_crawl4ai_stealth
        ]

        for i, method in enumerate(methods, 1):
            try:
                print(f"🔄 尝试方法 {i}: {method.__name__}")
                html_content = await method(url)

                if html_content and len(html_content) > 10000:  # 确保获取到完整页面
                    # 检查是否包含职位数据
                    if self._contains_job_data(html_content):
                        print(f"✅ 方法 {i} 成功获取数据")
                        return html_content
                    else:
                        print(f"⚠️ 方法 {i} 获取页面但无职位数据")
                else:
                    print(f"❌ 方法 {i} 获取页面失败或内容不完整")

            except Exception as e:
                print(f"❌ 方法 {i} 异常: {str(e)}")
                continue

        print("❌ 所有访问方法都失败了")
        return ""

    def _contains_job_data(self, html_content: str) -> bool:
        """检查页面是否包含职位数据"""
        indicators = [
            'job_detail',
            'job-list',
            'position-list',
            '职位',
            '招聘',
            'salary',
            'company'
        ]

        content_lower = html_content.lower()
        return any(indicator in content_lower for indicator in indicators)

    async def _method_crawl4ai_standard(self, url: str) -> str:
        """方法1: 标准crawl4ai访问"""
        result = await self.crawler.arun(
            url=url,
            timeout=30000,
            delay_before_return_html=2000
        )
        return result.html if result.success else ""

    async def _method_crawl4ai_with_js(self, url: str) -> str:
        """方法2: 带JavaScript的crawl4ai访问"""
        result = await self.crawler.arun(
            url=url,
            wait_for="css:.job-list",
            timeout=30000,
            delay_before_return_html=3000,
            js_code="""
            // 模拟用户行为
            window.scrollTo(0, document.body.scrollHeight);
            await new Promise(resolve => setTimeout(resolve, 2000));
            window.scrollTo(0, 0);
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 触发可能的懒加载
            const event = new Event('scroll');
            window.dispatchEvent(event);
            await new Promise(resolve => setTimeout(resolve, 1000));
            """
        )
        return result.html if result.success else ""

    async def _method_crawl4ai_with_security_bypass(self, url: str) -> str:
        """方法3: 集成BOSS安全绕过的访问"""
        if BOSS_SECURITY_BYPASS_AVAILABLE:
            try:
                # 使用Position_Crawler_1的安全绕过方法
                bypassed_html = await boss_security_bypass(url)
                if bypassed_html:
                    return bypassed_html
            except Exception as e:
                print(f"⚠️ 安全绕过方法异常: {e}")

        # 回退到标准方法
        return await self._method_crawl4ai_standard(url)

    async def _method_crawl4ai_stealth(self, url: str) -> str:
        """方法4: 隐身模式访问"""
        result = await self.crawler.arun(
            url=url,
            timeout=45000,
            delay_before_return_html=5000,
            js_code="""
            // 深度隐身脚本
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });

            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });

            // 模拟真实用户交互
            const randomDelay = () => Math.random() * 1000 + 500;

            await new Promise(resolve => setTimeout(resolve, randomDelay()));
            window.scrollTo(0, window.innerHeight / 2);
            await new Promise(resolve => setTimeout(resolve, randomDelay()));
            window.scrollTo(0, document.body.scrollHeight);
            await new Promise(resolve => setTimeout(resolve, randomDelay()));
            window.scrollTo(0, 0);
            await new Promise(resolve => setTimeout(resolve, randomDelay()));
            """
        )
        return result.html if result.success else ""

    async def crawl_search_page(self, city_code: str = "", keyword: str = "", page: int = 1) -> List[str]:
        """爬取搜索页面获取职位URL（集成BOSS安全绕过）"""
        async with self.semaphore:
            try:
                # 构造搜索URL
                params = {
                    'query': keyword,
                    'city': city_code,
                    'page': page
                }

                # 过滤空参数
                params = {k: v for k, v in params.items() if v}

                if params:
                    search_url = f"{self.search_url}?" + "&".join([f"{k}={v}" for k, v in params.items()])
                else:
                    search_url = self.search_url

                print(f"🔍 爬取页面: {search_url}")

                # 尝试多种方法获取页面内容
                html_content = await self._try_multiple_access_methods(search_url)

                if html_content:
                    self.stats['total_pages_crawled'] += 1

                    # 提取职位URL
                    job_urls = self.extract_job_urls_from_html(html_content)
                    self.stats['total_urls_found'] += len(job_urls)

                    print(f"✅ 页面爬取成功，找到 {len(job_urls)} 个职位URL")

                    # 如果没找到URL，保存页面用于调试
                    if not job_urls:
                        debug_file = f"debug_url_crawler_{int(time.time())}.html"
                        with open(debug_file, 'w', encoding='utf-8') as f:
                            f.write(html_content)
                        print(f"🔍 调试: 页面已保存到 {debug_file}")

                    return job_urls
                else:
                    print(f"❌ 页面爬取失败: {search_url}")
                    self.stats['failed_requests'] += 1
                    return []

            except Exception as e:
                print(f"❌ 爬取异常: {str(e)}")
                self.stats['failed_requests'] += 1
                return []

            finally:
                # 随机延迟
                await asyncio.sleep(random.uniform(1, 3))
                
                if result.success:
                    self.stats['total_pages_crawled'] += 1

                    # 提取职位URL
                    job_urls = self.extract_job_urls_from_html(result.html)
                    self.stats['total_urls_found'] += len(job_urls)

                    print(f"✅ 页面爬取成功，找到 {len(job_urls)} 个职位URL")

                    # 如果没找到URL，保存页面用于调试
                    if not job_urls:
                        debug_file = f"debug_crawl4ai_{int(time.time())}.html"
                        with open(debug_file, 'w', encoding='utf-8') as f:
                            f.write(result.html)
                        print(f"🔍 调试: 页面已保存到 {debug_file}")

                    return job_urls
                else:
                    print(f"❌ 页面爬取失败: {search_url}")
                    self.stats['failed_requests'] += 1
                    return []
                    
            except Exception as e:
                print(f"❌ 爬取异常: {str(e)}")
                self.stats['failed_requests'] += 1
                return []
            
            finally:
                # 随机延迟
                await asyncio.sleep(random.uniform(1, 3))

    async def crawl_all_pages_for_search(self, city_code: str = "", keyword: str = "", max_pages: int = 50) -> List[str]:
        """爬取某个搜索条件下的所有页面"""
        all_urls = []
        
        for page in range(1, max_pages + 1):
            print(f"📄 爬取第 {page} 页 (城市: {city_code or '全国'}, 关键词: {keyword or '全部'})")
            
            page_urls = await self.crawl_search_page(city_code, keyword, page)
            
            if not page_urls:
                print(f"⚠️ 第 {page} 页无数据，停止翻页")
                break
                
            all_urls.extend(page_urls)
            
            # 检查是否有重复URL（可能已到最后一页）
            if page > 1 and len(set(page_urls) & set(all_urls[:-len(page_urls)])) > len(page_urls) * 0.8:
                print(f"⚠️ 第 {page} 页重复率过高，可能已到最后一页")
                break
        
        return all_urls

    async def crawl_all_job_urls(self, cities: List[str] = None, keywords: List[str] = None, max_pages_per_search: int = 20) -> Set[str]:
        """爬取所有职位URL的主方法"""
        self.stats['start_time'] = time.time()
        
        # 使用默认值
        if cities is None:
            cities = list(self.city_codes.keys())[:5]  # 前5个主要城市
        if keywords is None:
            keywords = self.job_keywords[:10]  # 前10个热门关键词
        
        print(f"🚀 开始爬取所有职位URL")
        print(f"📊 城市数量: {len(cities)}")
        print(f"📊 关键词数量: {len(keywords)}")
        print(f"📊 预计搜索组合: {len(cities) * len(keywords)}")
        print("=" * 60)
        
        # 创建爬取任务
        tasks = []
        
        # 1. 按城市+关键词组合爬取
        for city in cities:
            city_code = self.city_codes.get(city, "")
            for keyword in keywords:
                task = self.crawl_all_pages_for_search(city_code, keyword, max_pages_per_search)
                tasks.append(task)
        
        # 2. 添加无关键词的城市搜索
        for city in cities:
            city_code = self.city_codes.get(city, "")
            task = self.crawl_all_pages_for_search(city_code, "", max_pages_per_search)
            tasks.append(task)
        
        # 3. 添加无城市限制的关键词搜索
        for keyword in keywords:
            task = self.crawl_all_pages_for_search("", keyword, max_pages_per_search)
            tasks.append(task)
        
        # 执行所有任务
        print(f"🔄 开始执行 {len(tasks)} 个爬取任务...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 收集所有URL
        for result in results:
            if isinstance(result, list):
                self.collected_urls.update(result)
            elif isinstance(result, Exception):
                print(f"❌ 任务执行异常: {str(result)}")
        
        self.stats['end_time'] = time.time()
        self.stats['unique_urls'] = len(self.collected_urls)
        
        return self.collected_urls

    def save_urls_to_file(self, filename: str = "boss_zhipin_job_urls.txt") -> str:
        """保存URL到文件"""
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        
        # 按URL排序
        sorted_urls = sorted(list(self.collected_urls))
        
        with open(filepath, 'w', encoding='utf-8') as f:
            for url in sorted_urls:
                f.write(url + '\n')
        
        print(f"💾 URL已保存到: {filepath}")
        print(f"📊 总计URL数量: {len(sorted_urls)}")
        
        return filepath

    def print_statistics(self):
        """打印统计信息"""
        duration = self.stats['end_time'] - self.stats['start_time'] if self.stats['end_time'] and self.stats['start_time'] else 0
        
        print("\n" + "=" * 60)
        print("📊 爬取统计报告")
        print("=" * 60)
        print(f"⏱️  总耗时: {duration:.2f} 秒")
        print(f"📄 爬取页面数: {self.stats['total_pages_crawled']}")
        print(f"🔗 发现URL总数: {self.stats['total_urls_found']}")
        print(f"✨ 去重后URL数: {self.stats['unique_urls']}")
        print(f"❌ 失败请求数: {self.stats['failed_requests']}")
        if self.stats['total_pages_crawled'] > 0:
            print(f"📈 平均每页URL数: {self.stats['total_urls_found'] / self.stats['total_pages_crawled']:.1f}")
        print("=" * 60)

# 使用示例函数
async def main():
    """主函数示例"""
    async with BOSSURLCrawler() as crawler:
        # 爬取所有职位URL
        urls = await crawler.crawl_all_job_urls(
            cities=["北京", "上海", "深圳", "杭州"],  # 可自定义城市
            keywords=["Python", "Java", "前端", "产品经理"],  # 可自定义关键词
            max_pages_per_search=10  # 每个搜索最大页数
        )
        
        # 保存结果
        crawler.save_urls_to_file()
        
        # 打印统计
        crawler.print_statistics()

if __name__ == "__main__":
    asyncio.run(main())
