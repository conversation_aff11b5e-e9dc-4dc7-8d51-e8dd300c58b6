"""
BOSS直聘职位URL爬虫 - 最终版
完全基于Position_Crawler_1项目核心技术
采用搜索页面+公司页面策略获取所有职位URL
"""

import asyncio
import time
import random
import json
import os
import re
from typing import List, Dict, Set, Any
from urllib.parse import urljoin, urlparse, quote
from bs4 import BeautifulSoup

# 导入核心依赖
try:
    from crawl4ai import AsyncWebCrawler, BrowserConfig
    CRAWL4AI_AVAILABLE = True
except ImportError:
    print("❌ crawl4ai未安装，请运行: pip install crawl4ai")
    CRAWL4AI_AVAILABLE = False

# 导入Position_Crawler_1的核心模块
try:
    import sys
    sys.path.append('/Users/<USER>/Position_Crawler_1')
    from boss_security_bypass import boss_security_bypass
    POSITION_CRAWLER_MODULES_AVAILABLE = True
    print("🚀 Position_Crawler_1安全绕过模块已加载")
except ImportError as e:
    print(f"⚠️ Position_Crawler_1模块导入失败: {e}")
    POSITION_CRAWLER_MODULES_AVAILABLE = False

class BOSSURLCrawlerFinal:
    """BOSS直聘职位URL爬虫 - 最终版"""
    
    def __init__(self):
        self.base_url = "https://www.zhipin.com"
        self.search_url = "https://www.zhipin.com/web/geek/job"
        self.crawler = None
        self.semaphore = asyncio.Semaphore(2)  # 降低并发避免被封
        
        # 收集到的所有职位URL
        self.collected_urls = set()
        
        # 城市代码（主要城市）
        self.city_codes = {
            "北京": "101010100", "上海": "101020100", "广州": "101280100",
            "深圳": "101280600", "杭州": "101210100", "南京": "101190100",
            "武汉": "101200100", "成都": "*********", "西安": "*********",
            "重庆": "*********"
        }
        
        # 热门关键词
        self.keywords = [
            "Python", "Java", "JavaScript", "前端", "后端", "产品经理",
            "运营", "销售", "UI设计师", "数据分析", "算法工程师",
            "测试工程师", "运维工程师", "人工智能", "机器学习"
        ]
        
        # 统计信息
        self.stats = {
            'search_pages_crawled': 0,
            'company_pages_crawled': 0,
            'total_urls_found': 0,
            'unique_urls': 0,
            'failed_requests': 0,
            'start_time': None,
            'end_time': None
        }

    async def __aenter__(self):
        """异步上下文管理器入口"""
        if not CRAWL4AI_AVAILABLE:
            raise ImportError("crawl4ai不可用")
            
        # 使用Position_Crawler_1的优化配置
        browser_config = BrowserConfig(
            headless=True,
            browser_type="chromium",
            viewport_width=1920,
            viewport_height=1080,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            extra_args=[
                # Position_Crawler_1的核心反检测配置
                "--no-sandbox",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-gpu",
                "--disable-dev-shm-usage",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",
                "--lang=zh-CN",
                "--accept-lang=zh-CN,zh;q=0.9,en;q=0.8",
                
                # 深度反检测
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-automation",
                "--disable-client-side-phishing-detection",
                "--disable-component-extensions-with-background-pages",
                "--disable-default-apps",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                "--metrics-recording-only",
                "--no-first-run",
                "--safebrowsing-disable-auto-update",
                "--password-store=basic",
                "--use-mock-keychain"
            ],
            headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache",
                "Referer": "https://www.zhipin.com/",
                "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1"
            }
        )
        
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.crawler:
            await self.crawler.__aexit__(exc_type, exc_val, exc_tb)

    def extract_job_urls_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取职位URL"""
        soup = BeautifulSoup(html_content, 'html.parser')
        job_urls = []
        
        # 多种选择器策略（基于Position_Crawler_1的经验）
        selectors = [
            "a[href*='/job_detail/']",
            "a[href*='job_detail']",
            ".job-list a[href*='job']",
            ".job-item a[href*='job']",
            ".job-card a[href*='job']",
            "a[ka='search_list_job']",
            "a[data-jobid]",
            ".job-primary a",
            ".job-title a"
        ]
        
        for selector in selectors:
            try:
                links = soup.select(selector)
                for link in links:
                    href = link.get('href')
                    if href and '/job_detail/' in href:
                        full_url = urljoin(self.base_url, href)
                        if self.is_valid_job_url(full_url):
                            job_urls.append(full_url)
            except Exception as e:
                continue
        
        # 从文本中提取URL（正则表达式）
        url_patterns = [
            r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]+\.html',
            r'/job_detail/[a-zA-Z0-9]+\.html'
        ]
        
        for pattern in url_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if not match.startswith('http'):
                    match = urljoin(self.base_url, match)
                if self.is_valid_job_url(match):
                    job_urls.append(match)
        
        return list(set(job_urls))

    def is_valid_job_url(self, url: str) -> bool:
        """验证是否为有效的职位URL"""
        if not url:
            return False
        pattern = r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]+\.html'
        return bool(re.match(pattern, url))

    async def crawl_with_security_bypass(self, url: str) -> str:
        """使用Position_Crawler_1的安全绕过方法爬取页面"""
        methods = [
            self._method_position_crawler_bypass,
            self._method_standard_crawl,
            self._method_with_js_simulation,
            self._method_stealth_mode
        ]
        
        for i, method in enumerate(methods, 1):
            try:
                print(f"🔄 尝试方法 {i}: {method.__name__}")
                html_content = await method(url)
                
                if html_content and len(html_content) > 5000:  # 确保获取到完整页面
                    if self._contains_job_data(html_content):
                        print(f"✅ 方法 {i} 成功获取数据")
                        return html_content
                    else:
                        print(f"⚠️ 方法 {i} 获取页面但无职位数据")
                else:
                    print(f"❌ 方法 {i} 获取页面失败或内容不完整")
                    
            except Exception as e:
                print(f"❌ 方法 {i} 异常: {str(e)}")
                continue
        
        print("❌ 所有访问方法都失败了")
        return ""

    def _contains_job_data(self, html_content: str) -> bool:
        """检查页面是否包含职位数据"""
        indicators = [
            'job-list',
            'job-item',
            'job-card',
            'job_detail',
            '职位',
            '招聘',
            'position',
            'salary'
        ]
        
        content_lower = html_content.lower()
        return any(indicator in content_lower for indicator in indicators)

    async def _method_position_crawler_bypass(self, url: str) -> str:
        """方法1: 使用Position_Crawler_1的安全绕过"""
        if POSITION_CRAWLER_MODULES_AVAILABLE:
            try:
                # 尝试多种绕过方法
                methods = [
                    'method_wait_and_extract_token',
                    'method_simulate_security_flow', 
                    'method_direct_bypass'
                ]
                
                for method_name in methods:
                    if hasattr(boss_security_bypass, method_name):
                        method = getattr(boss_security_bypass, method_name)
                        html_content = await method(url)
                        if html_content and len(html_content) > 1000:
                            return html_content
                            
            except Exception as e:
                print(f"⚠️ Position_Crawler_1绕过失败: {e}")
        
        return ""

    async def _method_standard_crawl(self, url: str) -> str:
        """方法2: 标准爬取"""
        try:
            result = await self.crawler.arun(
                url=url,
                timeout=30000,
                delay_before_return_html=3000
            )
            return result.html if result.success else ""
        except Exception as e:
            print(f"❌ 标准爬取失败: {e}")
            return ""

    async def _method_with_js_simulation(self, url: str) -> str:
        """方法3: 带JavaScript模拟的爬取"""
        try:
            result = await self.crawler.arun(
                url=url,
                timeout=45000,
                delay_before_return_html=5000,
                js_code="""
                // 模拟真实用户行为
                const randomDelay = () => Math.random() * 1000 + 500;
                
                // 滚动页面
                await new Promise(resolve => setTimeout(resolve, randomDelay()));
                window.scrollTo(0, window.innerHeight / 2);
                
                await new Promise(resolve => setTimeout(resolve, randomDelay()));
                window.scrollTo(0, document.body.scrollHeight);
                
                await new Promise(resolve => setTimeout(resolve, randomDelay()));
                window.scrollTo(0, 0);
                
                // 触发事件
                const scrollEvent = new Event('scroll');
                window.dispatchEvent(scrollEvent);
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                """
            )
            return result.html if result.success else ""
        except Exception as e:
            print(f"❌ JS模拟爬取失败: {e}")
            return ""

    async def _method_stealth_mode(self, url: str) -> str:
        """方法4: 隐身模式爬取"""
        try:
            result = await self.crawler.arun(
                url=url,
                timeout=60000,
                delay_before_return_html=8000,
                js_code="""
                // 深度隐身脚本
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en'],
                });
                
                // 删除自动化标识
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
                
                // 模拟用户交互
                const simulateInteraction = async () => {
                    const delay = () => Math.random() * 2000 + 1000;
                    
                    await new Promise(resolve => setTimeout(resolve, delay()));
                    window.scrollTo(0, 200);
                    
                    await new Promise(resolve => setTimeout(resolve, delay()));
                    window.scrollTo(0, 600);
                    
                    await new Promise(resolve => setTimeout(resolve, delay()));
                    window.scrollTo(0, document.body.scrollHeight);
                    
                    await new Promise(resolve => setTimeout(resolve, delay()));
                    window.scrollTo(0, 0);
                };
                
                await simulateInteraction();
                """
            )
            return result.html if result.success else ""
        except Exception as e:
            print(f"❌ 隐身模式爬取失败: {e}")
            return ""

    async def crawl_search_page(self, city_code: str = "", keyword: str = "", page: int = 1) -> List[str]:
        """爬取搜索页面获取职位URL"""
        async with self.semaphore:
            try:
                # 构造搜索URL
                params = []
                if keyword:
                    params.append(f"query={quote(keyword)}")
                if city_code:
                    params.append(f"city={city_code}")
                if page > 1:
                    params.append(f"page={page}")

                if params:
                    search_url = f"{self.search_url}?" + "&".join(params)
                else:
                    search_url = self.search_url

                print(f"🔍 爬取搜索页面: {search_url}")

                # 使用安全绕过方法获取页面
                html_content = await self.crawl_with_security_bypass(search_url)

                if html_content:
                    self.stats['search_pages_crawled'] += 1

                    # 提取职位URL
                    job_urls = self.extract_job_urls_from_html(html_content)
                    self.stats['total_urls_found'] += len(job_urls)

                    print(f"✅ 搜索页面爬取成功，找到 {len(job_urls)} 个职位URL")

                    # 如果没找到URL，保存页面用于调试
                    if not job_urls:
                        debug_file = f"debug_search_{int(time.time())}.html"
                        with open(debug_file, 'w', encoding='utf-8') as f:
                            f.write(html_content)
                        print(f"🔍 调试: 搜索页面已保存到 {debug_file}")

                    return job_urls
                else:
                    print(f"❌ 搜索页面爬取失败: {search_url}")
                    self.stats['failed_requests'] += 1
                    return []

            except Exception as e:
                print(f"❌ 搜索页面爬取异常: {str(e)}")
                self.stats['failed_requests'] += 1
                return []

            finally:
                # 随机延迟避免被封
                await asyncio.sleep(random.uniform(3, 6))

    async def crawl_multiple_search_pages(self, city_code: str = "", keyword: str = "", max_pages: int = 10) -> List[str]:
        """爬取多页搜索结果"""
        all_urls = []

        for page in range(1, max_pages + 1):
            print(f"📄 爬取第 {page} 页 (城市: {city_code or '全国'}, 关键词: {keyword or '全部'})")

            page_urls = await self.crawl_search_page(city_code, keyword, page)

            if not page_urls:
                print(f"⚠️ 第 {page} 页无数据，停止翻页")
                break

            all_urls.extend(page_urls)

            # 检查重复率，判断是否到达最后一页
            if page > 1:
                overlap = len(set(page_urls) & set(all_urls[:-len(page_urls)]))
                if overlap > len(page_urls) * 0.8:
                    print(f"⚠️ 第 {page} 页重复率过高，可能已到最后一页")
                    break

        return all_urls

    async def crawl_all_job_urls(self, cities: List[str] = None, keywords: List[str] = None, max_pages_per_search: int = 5) -> Set[str]:
        """爬取所有职位URL的主方法"""
        self.stats['start_time'] = time.time()

        # 使用默认值
        if cities is None:
            cities = list(self.city_codes.keys())[:3]  # 前3个主要城市
        if keywords is None:
            keywords = self.keywords[:5]  # 前5个热门关键词

        print(f"🚀 开始爬取所有职位URL")
        print(f"📊 城市数量: {len(cities)}")
        print(f"📊 关键词数量: {len(keywords)}")
        print(f"📊 预计搜索组合: {len(cities) * len(keywords) + len(cities) + len(keywords)}")
        print("=" * 60)

        # 策略1: 城市+关键词组合搜索
        for city in cities:
            city_code = self.city_codes.get(city, "")
            for keyword in keywords:
                print(f"\n🎯 搜索: {city} + {keyword}")
                urls = await self.crawl_multiple_search_pages(city_code, keyword, max_pages_per_search)
                self.collected_urls.update(urls)
                print(f"📈 当前累计URL数: {len(self.collected_urls)}")

        # 策略2: 仅城市搜索（获取该城市所有职位）
        for city in cities:
            city_code = self.city_codes.get(city, "")
            print(f"\n🎯 搜索: {city} (全部职位)")
            urls = await self.crawl_multiple_search_pages(city_code, "", max_pages_per_search)
            self.collected_urls.update(urls)
            print(f"📈 当前累计URL数: {len(self.collected_urls)}")

        # 策略3: 仅关键词搜索（全国范围）
        for keyword in keywords:
            print(f"\n🎯 搜索: {keyword} (全国)")
            urls = await self.crawl_multiple_search_pages("", keyword, max_pages_per_search)
            self.collected_urls.update(urls)
            print(f"📈 当前累计URL数: {len(self.collected_urls)}")

        # 策略4: 无条件搜索（获取最新职位）
        print(f"\n🎯 搜索: 全部最新职位")
        urls = await self.crawl_multiple_search_pages("", "", max_pages_per_search)
        self.collected_urls.update(urls)

        self.stats['end_time'] = time.time()
        self.stats['unique_urls'] = len(self.collected_urls)

        return self.collected_urls

    def save_urls_to_file(self, filename: str = "boss_job_urls_final.txt") -> str:
        """保存URL到文件"""
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)

        filepath = os.path.join(output_dir, filename)
        sorted_urls = sorted(list(self.collected_urls))

        with open(filepath, 'w', encoding='utf-8') as f:
            for url in sorted_urls:
                f.write(url + '\n')

        print(f"💾 URL已保存到: {filepath}")
        print(f"📊 总计URL数量: {len(sorted_urls)}")

        return filepath

    def print_statistics(self):
        """打印统计信息"""
        duration = self.stats['end_time'] - self.stats['start_time'] if self.stats['end_time'] and self.stats['start_time'] else 0

        print("\n" + "=" * 60)
        print("📊 爬取统计报告")
        print("=" * 60)
        print(f"⏱️  总耗时: {duration:.2f} 秒")
        print(f"📄 爬取搜索页面: {self.stats['search_pages_crawled']}")
        print(f"🏢 爬取公司页面: {self.stats['company_pages_crawled']}")
        print(f"🔗 发现URL总数: {self.stats['total_urls_found']}")
        print(f"✨ 去重后URL数: {self.stats['unique_urls']}")
        print(f"❌ 失败请求数: {self.stats['failed_requests']}")
        if self.stats['search_pages_crawled'] > 0:
            print(f"📈 平均每页URL数: {self.stats['total_urls_found'] / self.stats['search_pages_crawled']:.1f}")
        print("=" * 60)

# 使用示例
async def main():
    """主函数示例"""
    async with BOSSURLCrawlerFinal() as crawler:
        # 爬取所有职位URL
        urls = await crawler.crawl_all_job_urls(
            cities=["北京", "上海", "深圳"],  # 可自定义城市
            keywords=["Python", "Java", "前端"],  # 可自定义关键词
            max_pages_per_search=3  # 每个搜索最大页数
        )

        # 保存结果
        crawler.save_urls_to_file()

        # 打印统计
        crawler.print_statistics()

if __name__ == "__main__":
    asyncio.run(main())
