"""
BOSS直聘职位URL爬虫 V2
完全基于Position_Crawler_1项目核心技术
采用种子URL扩展策略获取所有职位URL
"""

import asyncio
import time
import random
import json
import os
import re
from typing import List, Dict, Set, Any
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

# 导入核心依赖
try:
    from crawl4ai import AsyncWebCrawler, BrowserConfig
    CRAWL4AI_AVAILABLE = True
except ImportError:
    print("❌ crawl4ai未安装，请运行: pip install crawl4ai")
    CRAWL4AI_AVAILABLE = False

# 导入Position_Crawler_1的核心模块
try:
    import sys
    sys.path.append('/Users/<USER>/Position_Crawler_1')
    from boss_security_bypass import boss_security_bypass
    from data_extractor import data_extractor
    POSITION_CRAWLER_MODULES_AVAILABLE = True
    print("🚀 Position_Crawler_1核心模块已加载")
except ImportError as e:
    print(f"⚠️ Position_Crawler_1模块导入失败: {e}")
    POSITION_CRAWLER_MODULES_AVAILABLE = False

class BOSSURLCrawlerV2:
    """BOSS直聘职位URL爬虫V2 - 基于种子URL扩展策略"""
    
    def __init__(self):
        self.base_url = "https://www.zhipin.com"
        self.crawler = None
        self.semaphore = asyncio.Semaphore(3)
        
        # 种子URL（已知的真实职位URL）
        self.seed_urls = [
            "https://www.zhipin.com/job_detail/f9d8c7acea814efa1XB93NS5FlRQ.html",
        ]
        
        # 收集到的所有职位URL
        self.collected_urls = set()
        
        # 已处理的URL（避免重复处理）
        self.processed_urls = set()
        
        # 公司URL集合（用于发现更多职位）
        self.company_urls = set()
        
        # 统计信息
        self.stats = {
            'seed_urls_processed': 0,
            'company_pages_crawled': 0,
            'search_pages_crawled': 0,
            'total_urls_found': 0,
            'unique_urls': 0,
            'failed_requests': 0,
            'start_time': None,
            'end_time': None
        }

    async def __aenter__(self):
        """异步上下文管理器入口"""
        if not CRAWL4AI_AVAILABLE:
            raise ImportError("crawl4ai不可用")
            
        # 使用Position_Crawler_1的浏览器配置
        browser_config = BrowserConfig(
            headless=True,
            browser_type="chromium",
            viewport_width=1920,
            viewport_height=1080,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            extra_args=[
                # Position_Crawler_1的反检测配置
                "--no-sandbox",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-gpu",
                "--disable-dev-shm-usage",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",
                "--lang=zh-CN",
                "--accept-lang=zh-CN,zh;q=0.9,en;q=0.8",
                
                # 深度反检测
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-automation",
                "--disable-client-side-phishing-detection",
                "--disable-component-extensions-with-background-pages",
                "--disable-default-apps",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                "--metrics-recording-only",
                "--no-first-run",
                "--safebrowsing-disable-auto-update",
                "--password-store=basic",
                "--use-mock-keychain"
            ],
            headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache",
                "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Upgrade-Insecure-Requests": "1"
            }
        )
        
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.crawler:
            await self.crawler.__aexit__(exc_type, exc_val, exc_tb)

    def extract_job_urls_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取职位URL（使用Position_Crawler_1的策略）"""
        if POSITION_CRAWLER_MODULES_AVAILABLE:
            try:
                # 使用Position_Crawler_1的数据提取器
                urls = data_extractor.extract_job_urls_from_list(html_content)
                return urls
            except Exception as e:
                print(f"⚠️ Position_Crawler_1提取器失败，使用备用方法: {e}")
        
        # 备用提取方法
        return self._extract_job_urls_fallback(html_content)

    def _extract_job_urls_fallback(self, html_content: str) -> List[str]:
        """备用职位URL提取方法"""
        soup = BeautifulSoup(html_content, 'html.parser')
        job_urls = []
        
        # 多种选择器策略
        selectors = [
            "a[href*='/job_detail/']",
            "a[href*='job_detail']",
            "a[href*='/position/']",
            "a[href*='position']",
            "a[class*='job']",
            "a[class*='position']",
            ".job-list a",
            ".position-list a",
            ".job-item a",
            "a[data-job-id]",
            "a[data-position-id]"
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href') or link.get('data-href')
                if href and ('job_detail' in href or 'position' in href):
                    full_url = urljoin(self.base_url, href)
                    if self.is_valid_job_url(full_url):
                        job_urls.append(full_url)
        
        # 从文本中提取URL
        url_patterns = [
            r'https?://[^/]*zhipin\.com/job_detail/[^"\s]+',
            r'/job_detail/[^"\s]+',
        ]
        
        for pattern in url_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if not match.startswith('http'):
                    match = urljoin(self.base_url, match)
                if self.is_valid_job_url(match):
                    job_urls.append(match)
        
        return list(set(job_urls))

    def is_valid_job_url(self, url: str) -> bool:
        """验证是否为有效的职位URL"""
        if not url:
            return False
        pattern = r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]+\.html'
        return bool(re.match(pattern, url))

    def extract_company_urls(self, html_content: str) -> List[str]:
        """从HTML中提取公司URL"""
        soup = BeautifulSoup(html_content, 'html.parser')
        company_urls = []
        
        # 公司页面选择器
        selectors = [
            "a[href*='/company/']",
            "a[href*='company']",
            ".company-name a",
            ".company-info a",
            "a[class*='company']"
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href and 'company' in href:
                    full_url = urljoin(self.base_url, href)
                    company_urls.append(full_url)
        
        return list(set(company_urls))

    async def crawl_with_position_crawler_methods(self, url: str) -> str:
        """使用Position_Crawler_1的方法爬取页面"""
        if POSITION_CRAWLER_MODULES_AVAILABLE:
            try:
                # 尝试使用BOSS安全绕过
                html_content = await boss_security_bypass.method_wait_and_extract_token(url)
                if html_content and len(html_content) > 1000:
                    return html_content
                    
                html_content = await boss_security_bypass.method_simulate_security_flow(url)
                if html_content and len(html_content) > 1000:
                    return html_content
                    
                html_content = await boss_security_bypass.method_direct_bypass(url)
                if html_content and len(html_content) > 1000:
                    return html_content
                    
            except Exception as e:
                print(f"⚠️ Position_Crawler_1方法失败: {e}")
        
        # 回退到标准方法
        return await self._crawl_standard_method(url)

    async def _crawl_standard_method(self, url: str) -> str:
        """标准爬取方法"""
        try:
            result = await self.crawler.arun(
                url=url,
                timeout=30000,
                delay_before_return_html=3000,
                js_code="""
                // 模拟用户行为
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 2000));
                window.scrollTo(0, 0);
                await new Promise(resolve => setTimeout(resolve, 1000));
                """
            )
            return result.html if result.success else ""
        except Exception as e:
            print(f"❌ 标准方法失败: {e}")
            return ""

    async def discover_urls_from_seed(self, seed_url: str) -> Set[str]:
        """从种子URL发现新的职位URL"""
        async with self.semaphore:
            try:
                print(f"🌱 处理种子URL: {seed_url}")
                
                # 获取种子页面内容
                html_content = await self.crawl_with_position_crawler_methods(seed_url)
                
                if not html_content:
                    print(f"❌ 无法获取种子页面内容: {seed_url}")
                    return set()
                
                discovered_urls = set()
                
                # 1. 从当前页面提取职位URL
                job_urls = self.extract_job_urls_from_html(html_content)
                discovered_urls.update(job_urls)
                print(f"📄 从种子页面发现 {len(job_urls)} 个职位URL")
                
                # 2. 提取公司URL
                company_urls = self.extract_company_urls(html_content)
                self.company_urls.update(company_urls)
                print(f"🏢 发现 {len(company_urls)} 个公司URL")
                
                # 3. 从公司页面获取更多职位
                for company_url in list(company_urls)[:3]:  # 限制数量避免过度爬取
                    company_jobs = await self.discover_urls_from_company(company_url)
                    discovered_urls.update(company_jobs)
                
                self.stats['seed_urls_processed'] += 1
                await asyncio.sleep(random.uniform(2, 4))
                
                return discovered_urls
                
            except Exception as e:
                print(f"❌ 处理种子URL异常: {e}")
                self.stats['failed_requests'] += 1
                return set()

    async def discover_urls_from_company(self, company_url: str) -> Set[str]:
        """从公司页面发现职位URL"""
        try:
            print(f"🏢 处理公司页面: {company_url}")
            
            html_content = await self.crawl_with_position_crawler_methods(company_url)
            
            if html_content:
                job_urls = self.extract_job_urls_from_html(html_content)
                self.stats['company_pages_crawled'] += 1
                print(f"🏢 从公司页面发现 {len(job_urls)} 个职位URL")
                return set(job_urls)
            
            return set()
            
        except Exception as e:
            print(f"❌ 处理公司页面异常: {e}")
            return set()

    async def crawl_all_job_urls(self, max_iterations: int = 5) -> Set[str]:
        """爬取所有职位URL的主方法"""
        self.stats['start_time'] = time.time()
        
        print(f"🚀 开始基于种子URL的职位URL发现")
        print(f"🌱 种子URL数量: {len(self.seed_urls)}")
        print("=" * 60)
        
        # 从种子URL开始扩展
        for iteration in range(max_iterations):
            print(f"\n🔄 第 {iteration + 1} 轮发现")
            
            # 处理种子URL
            for seed_url in self.seed_urls:
                if seed_url not in self.processed_urls:
                    discovered = await self.discover_urls_from_seed(seed_url)
                    self.collected_urls.update(discovered)
                    self.processed_urls.add(seed_url)
            
            # 使用新发现的URL作为下一轮的种子
            new_seeds = list(self.collected_urls - self.processed_urls)[:10]  # 限制数量
            if not new_seeds:
                print("⚠️ 没有新的URL可以处理，停止发现")
                break
                
            self.seed_urls.extend(new_seeds)
            print(f"📈 本轮新增种子URL: {len(new_seeds)}")
            print(f"📊 累计发现URL: {len(self.collected_urls)}")
        
        self.stats['end_time'] = time.time()
        self.stats['unique_urls'] = len(self.collected_urls)
        self.stats['total_urls_found'] = len(self.collected_urls)
        
        return self.collected_urls

    def save_urls_to_file(self, filename: str = "boss_job_urls_v2.txt") -> str:
        """保存URL到文件"""
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        sorted_urls = sorted(list(self.collected_urls))
        
        with open(filepath, 'w', encoding='utf-8') as f:
            for url in sorted_urls:
                f.write(url + '\n')
        
        print(f"💾 URL已保存到: {filepath}")
        print(f"📊 总计URL数量: {len(sorted_urls)}")
        
        return filepath

    def print_statistics(self):
        """打印统计信息"""
        duration = self.stats['end_time'] - self.stats['start_time'] if self.stats['end_time'] and self.stats['start_time'] else 0
        
        print("\n" + "=" * 60)
        print("📊 爬取统计报告")
        print("=" * 60)
        print(f"⏱️  总耗时: {duration:.2f} 秒")
        print(f"🌱 处理种子URL: {self.stats['seed_urls_processed']}")
        print(f"🏢 爬取公司页面: {self.stats['company_pages_crawled']}")
        print(f"🔗 发现URL总数: {self.stats['total_urls_found']}")
        print(f"✨ 去重后URL数: {self.stats['unique_urls']}")
        print(f"❌ 失败请求数: {self.stats['failed_requests']}")
        print("=" * 60)

# 使用示例
async def main():
    """主函数示例"""
    async with BOSSURLCrawlerV2() as crawler:
        # 爬取所有职位URL
        urls = await crawler.crawl_all_job_urls(max_iterations=3)
        
        # 保存结果
        crawler.save_urls_to_file()
        
        # 打印统计
        crawler.print_statistics()

if __name__ == "__main__":
    asyncio.run(main())
